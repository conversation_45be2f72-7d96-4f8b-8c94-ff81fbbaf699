<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Fx3>
<Fx3 version="1.2.1">
    <ProgramList>
        <Name>E:/TBBReDiff_trans_heat/avg9/0_TBB_Data/TBB_refine_1.645e5/Budget/Budget_PS/Fortran/Budget3d.exe</Name>
        <Arguments></Arguments>
        <WorkingDirectory>E:/TBBReDiff_trans_heat/avg9/0_TBB_Data/TBB_refine_1.645e5/Budget/Budget_PS/Fortran</WorkingDirectory>
        <EnvironmentList></EnvironmentList>
        <Script></Script>
        <Type>append</Type>
    </ProgramList>
    <AddPaths>
        <Path>&quot;E:/TBBReDiff_trans_heat/avg9/0_TBB_Data/TBB_refine_1.645e5/Budget/Budget_PS/Fortran&quot;</Path>
    </AddPaths>
    <WatchVariables>
        <Variable>X</Variable>
        <Variable>U_mean</Variable>
        <Variable>f</Variable>
        <Variable>U_fluc</Variable>
        <Variable>uv</Variable>
        <Variable>k_theta</Variable>
        <Variable>k</Variable>
        <Variable>eps</Variable>
        <Variable>dx_arr</Variable>
        <Variable>eps_k</Variable>
        <Variable>Pk</Variable>
        <Variable>Y</Variable>
        <Variable>uu</Variable>
        <Variable>du_dy</Variable>
        <Variable>dk_dx</Variable>
    </WatchVariables>
    <BreakpointList>
        <Breakpoint>
            <id>1</id>
            <line>92</line>
            <filename>E:/TBBReDiff_trans_heat/avg9/0_TBB_Data/TBB_refine_1.645e5/Budget/Budget_PS/Fortran\fcontrl.f95</filename>
            <type>breakpoint</type>
            <disposition></disposition>
            <enabled>y</enabled>
            <address>0X0000000140003A0A</address>
            <what></what>
            <times>0</times>
            <ignore>0</ignore>
            <condition></condition>
            <threads></threads>
        </Breakpoint>
    </BreakpointList>
    <Memory>
        <address></address>
        <bytes>1</bytes>
        <column>1</column>
        <word>1</word>
        <checked>N</checked>
    </Memory>
    <IgnoredSources>
        <IgnoredList/>
        <Unlocatable>N</Unlocatable>
    </IgnoredSources>
    <Arguments>
        <Argument></Argument>
    </Arguments>
</Fx3>
