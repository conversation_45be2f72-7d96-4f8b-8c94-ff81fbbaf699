import pandas as pd
import os

def convert_to_tecplot(input_dir, output_dir):
    """将Excel文件转换为Tecplot格式"""
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取所有Excel文件
    excel_files = [f for f in os.listdir(input_dir) if f.endswith('.xlsx')]
    
    # 分别处理TKE和温度预算文件
    tke_files = [f for f in excel_files if 'TKE_Budget' in f]
    temp_files = [f for f in excel_files if 'Temperature_Budget' in f]
    
    # 处理TKE预算文件
    for excel_file in tke_files:
        i_value = float(excel_file.split('i')[1].split('.xlsx')[0])
        input_path = os.path.join(input_dir, excel_file)
        output_path = os.path.join(output_dir, f'TKE_Budget_i{i_value}.dat')
        
        # 读取Excel文件
        df = pd.read_excel(input_path)
        df = df.head(171)  # 确保只取前171行
        
        # 写入Tecplot格式文件
        with open(output_path, 'w') as f:
            # 写入标题
            f.write('TITLE = "TKE Budget Terms"\n')
            # 写入变量
            f.write('VARIABLES = "Y", "Ck", "Pk", "Tk", "Dk", "eps_k", "Pi_k"\n')
            # 写入区域信息
            f.write(f'ZONE T="i={i_value}", I={len(df)}, F=POINT\n')
            # 写入数据
            for _, row in df.iterrows():
                f.write(f"{row['Y']:.8E} {row['Ck']:.8E} {row['Pk']:.8E} {row['Tk']:.8E} {row['Dk']:.8E} {row['eps_k']:.8E} {row['Pi_k']:.8E}\n")
    
    # 处理温度预算文件
    for excel_file in temp_files:
        i_value = float(excel_file.split('i')[1].split('.xlsx')[0])
        input_path = os.path.join(input_dir, excel_file)
        output_path = os.path.join(output_dir, f'Temperature_Budget_i{i_value}.dat')
        
        # 读取Excel文件
        df = pd.read_excel(input_path)
        df = df.head(171)  # 确保只取前171行
        
        # 写入Tecplot格式文件
        with open(output_path, 'w') as f:
            # 写入标题
            f.write('TITLE = "Temperature Budget Terms"\n')
            # 写入变量
            f.write('VARIABLES = "Y", "Ck_theta", "Pk_theta", "Tk_theta", "Dk_theta", "eps_k_theta"\n')
            # 写入区域信息
            f.write(f'ZONE T="i={i_value}", I={len(df)}, F=POINT\n')
            # 写入数据
            for _, row in df.iterrows():
                f.write(f"{row['Y']:.8E} {row['Ck_theta']:.8E} {row['Pk_theta']:.8E} {row['Tk_theta']:.8E} {row['Dk_theta']:.8E} {row['eps_k_theta']:.8E}\n")

def main():
    # 设置输入和输出目录
    input_dir = 'Budget_Lines'
    output_dir = 'Budget_Lines'
    
    # 转换文件
    convert_to_tecplot(input_dir, output_dir)
    print("转换完成！")

if __name__ == '__main__':
    main() 