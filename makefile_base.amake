
#
# Absoft Developer Tools Interface requires use of this makefile.
#
.SHELL: name="cmd.exe"
MKDIR=if not exist "$(OBJDIR)" mkdir 
RMDIR=rmdir /S /Q

#
# Variables for the compiler(s) and linker
#
ABSOFT_F95=f95 
ABSOFT_F77=f95
VAST_PREPROCESSOR=vastfp.exe
RC=rc.exe
CL=cl.exe
MAKE=amake2
ABSOFT_F95_LINKER=f95
DUMMY=

#
# Flags
#
ABSOFT_F95_FLAGS= -g -m64 -mcmodel=medium -stack:32768000
ABSOFT_F77_FLAGS= -f77_compat -g -m64 -mcmodel=medium -stack:32768000
RC_FLAGS=
CL_FLAGS= -Zi
MAKE_FLAGS= -k
ABSOFT_F95_LINKER_FLAGS=-IPA:debug=on -IPA:source_pu_order=on          -stack:32768000 -m64 -mcmodel=medium -g

#
# Name of the executable
#
TARGET=Budget3d.exe
OUTDIR=.
OBJDIR=.\Debug

#
# Files for this executable
#
VAST_PREPROCESSOR_FILES=
ABSOFT_F95_FILES="fcontrl.f95" "fmains.f95" "fmdus.f95" "fsolves.f95"
ABSOFT_F77_FILES=
CL_FILES=
ABSOFT_F95_LINKER_FILES=
RC_FILES=
MAKE_FILES=

#
# Rules and dependencies for the executable
#
SRCFILES=$(ABSOFT_F95_FILES)
OBJFILES="$(OBJDIR)\fcontrl.obj" "$(OBJDIR)\fmains.obj" "$(OBJDIR)\fmdus.obj" "$(OBJDIR)\fsolves.obj"
all: "$(OBJDIR)" "$(OUTDIR)\$(TARGET)"
	

"$(OUTDIR)\$(TARGET)":  $(OBJFILES) $(ABSOFT_F95_LINKER_FILES) $(MODFILES) $(RC_FILES)
	$(ABSOFT_F95_LINKER)  $(LPATHS) $(OBJFILES) $(ABSOFT_F95_LINKER_FILES) $(LIBS) -o "$(OUTDIR)\$(TARGET)" $(ABSOFT_F95_LINKER_FLAGS)
	IF %errorlevel% NEQ 0 EXIT 1



"$(OBJDIR)\fcontrl.obj": "fcontrl.f95"
	$(ABSOFT_F95) -c -nowdir -YLOOP_ANALYZER -LNO:simd_verbose=on -LNO:apo_verbose=on -CG:filetable_verbose=on $(ABSOFT_F95_FLAGS) -o "$(OBJDIR)\fcontrl.obj" "fcontrl.f95"
	IF %errorlevel% NEQ 0 EXIT 1

"$(OBJDIR)\fmains.obj": "fmains.f95"
	$(ABSOFT_F95) -c -nowdir -YLOOP_ANALYZER -LNO:simd_verbose=on -LNO:apo_verbose=on -CG:filetable_verbose=on $(ABSOFT_F95_FLAGS) -o "$(OBJDIR)\fmains.obj" "fmains.f95"
	IF %errorlevel% NEQ 0 EXIT 1

"$(OBJDIR)\fmdus.obj": "fmdus.f95"
	$(ABSOFT_F95) -c -nowdir -YLOOP_ANALYZER -LNO:simd_verbose=on -LNO:apo_verbose=on -CG:filetable_verbose=on $(ABSOFT_F95_FLAGS) -o "$(OBJDIR)\fmdus.obj" "fmdus.f95"
	IF %errorlevel% NEQ 0 EXIT 1

"$(OBJDIR)\fsolves.obj": "fsolves.f95"
	$(ABSOFT_F95) -c -nowdir -YLOOP_ANALYZER -LNO:simd_verbose=on -LNO:apo_verbose=on -CG:filetable_verbose=on $(ABSOFT_F95_FLAGS) -o "$(OBJDIR)\fsolves.obj" "fsolves.f95"
	IF %errorlevel% NEQ 0 EXIT 1

"$(OBJDIR)":
	$(MKDIR) "$(OBJDIR)"
#
# Clean Rules
#
clean:
	 if exist "$(OBJDIR)" $(RMDIR) "$(OBJDIR)" 
	 if exist "$(TARGET)" erase "$(TARGET)"

#
# Define necessary macros for dependency scanning.
#
MKDEP=amakedepend
MKDEP_FLAGS= -quiet "-pre$(OBJDIR)\"\" -Y -modInfo -errdel -f90 -f makefile.amake
#
# Generate dependencies for the project
#
ScanAll: "$(OUTDIR)" "$(OBJDIR)" MakeDepsAll

#
# Scan for all F77, F95, and c/c++ dependencies
#
MakeDepsAll:
	$(MKDEP) -quiet "-pre.\Debug\\" -Y -modInfo -errdel -f90 -f makefile.amake -a -info  -g -m64 -mcmodel=medium -stack:32768000 -info "fcontrl.f95" "fmains.f95" "fmdus.f95" "fsolves.f95" 
