import numpy as np
import pandas as pd
import os
import re

def parse_scientific_number(s):
    """解析科学计数法格式的数字字符串"""
    # 如果字符串中已经包含E或e，直接转换
    if 'E' in s or 'e' in s:
        return float(s)
    
    # 处理没有E或e的科学计数法格式
    # 例如: "-0.65409105-307" 应该转换为 "-0.65409105E-307"
    match = re.match(r'([+-]?\d*\.?\d+)([+-]\d+)', s)
    if match:
        base, exponent = match.groups()
        return float(f"{base}E{exponent}")
    
    # 如果不是科学计数法格式，直接转换
    return float(s)

def read_budget_terms(filename):
    """读取预算项数据文件"""
    all_data = {}  # 使用字典存储每个i值的数据
    current_i = None
    current_i_data = []
    point_count = 0  # 用于计数每个i值下的点数
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:  # 空行
                continue
            if line.startswith('#'):  # 注释行
                if 'i =' in line:  # 新的i值开始
                    # 保存前一个i的数据（如果存在）
                    if current_i is not None and current_i_data:
                        all_data[current_i] = np.array(current_i_data)
                    # 重置计数和临时存储
                    current_i = int(line.split('=')[1].strip())
                    current_i_data = []
                    point_count = 0
                continue
            
            # 处理数据行
            try:
                values = [parse_scientific_number(x) for x in line.split()]
                if values and point_count < 171:  # 只添加前171个点
                    current_i_data.append(values)
                    point_count += 1
            except ValueError as e:
                print(f"警告：无法解析行：{line}")
                print(f"错误信息：{str(e)}")
                continue
    
    # 处理最后一组数据
    if current_i is not None and current_i_data:
        all_data[current_i] = np.array(current_i_data)
    
    # 将所有数据合并成一个数组
    data = []
    for i in sorted(all_data.keys()):
        data.extend(all_data[i])
    
    return np.array(data)

def process_and_save_excel(data, output_dir):
    """处理数据并保存为Excel文件"""
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取唯一的i值
    i_values = np.unique(data[:, 0])
    
    # 为每个i创建Excel文件
    for i in i_values:
        # 获取当前i的数据
        i_data = data[data[:, 0] == i]
        
        # 创建TKE预算项DataFrame
        tke_df = pd.DataFrame({
            'Y': i_data[:, 1],  # y值
            'Ck': i_data[:, 2],  # Ck
            'Pk': i_data[:, 3],  # Pk
            'Tk': i_data[:, 4],  # Tk
            'Dk': i_data[:, 5],  # Dk
            'eps_k': i_data[:, 6],  # eps_k
            'Pi_k': i_data[:, 7]   # Pi_k
        })
        
        # 创建温度预算项DataFrame
        temp_df = pd.DataFrame({
            'Y': i_data[:, 1],        # y值
            'Ck_theta': i_data[:, 8],  # Ck_theta
            'Pk_theta': i_data[:, 9],  # Pk_theta
            'Tk_theta': i_data[:, 10], # Tk_theta
            'Dk_theta': i_data[:, 11], # Dk_theta
            'eps_k_theta': i_data[:, 12] # eps_k_theta
        })
        
        # 保存为Excel文件
        tke_filename = os.path.join(output_dir, f'TKE_Budget_i{i}.xlsx')
        temp_filename = os.path.join(output_dir, f'Temperature_Budget_i{i}.xlsx')
        
        tke_df.to_excel(tke_filename, index=False)
        temp_df.to_excel(temp_filename, index=False)
        
        print(f"已保存i={i}的预算项数据（共{len(i_data)}个点）")

def main():
    # 输入文件
    input_file = 'Budget_Terms_All.dat'
    # 输出目录
    output_dir = 'Budget_Lines'
    
    # 读取数据
    print("正在读取数据...")
    data = read_budget_terms(input_file)
    
    # 处理并保存数据
    print("正在处理数据并保存为Excel文件...")
    process_and_save_excel(data, output_dir)
    
    print("处理完成！")

if __name__ == '__main__':
    main() 